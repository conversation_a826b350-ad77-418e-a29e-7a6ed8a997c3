# Local SFTP Server Setup Guide for Testing

## Your Test Configuration
- **SFTP Host**: ************* (your Mac)
- **Username**: ramchandra.kulkarni
- **Password**: MayRam@2024
- **SFTP Directory**: `/Users/<USER>/sftp_uploads/Outbound`
- **Port**: 22

## Prerequisites Setup

### 1. Enable SSH/SFTP on Your Mac
```bash
# Enable Remote Login (SSH/SFTP) on your Mac
sudo systemsetup -setremotelogin on

# Verify SSH is running
sudo launchctl list | grep ssh
```

### 2. Create Test Directory Structure
```bash
# Create the SFTP upload directories
mkdir -p /Users/<USER>/sftp_uploads/Outbound
mkdir -p /Users/<USER>/sftp_uploads/Inbound

# Set proper permissions
chmod 755 /Users/<USER>/sftp_uploads
chmod 755 /Users/<USER>/sftp_uploads/Outbound
chmod 755 /Users/<USER>/sftp_uploads/Inbound
```

### 3. Create Test PGP Files
You'll need to create some test .pgp files in the Outbound folder. Here's how:

```bash
# Navigate to the Outbound directory
cd /Users/<USER>/sftp_uploads/Outbound

# Create a sample CSV file
cat > sample_data.csv << EOF
name,age,city,salary,department
John Doe,30,New York,75000,Engineering
Jane Smith,25,San Francisco,80000,Marketing
Bob Johnson,35,Chicago,70000,Sales
Alice Brown,28,Boston,85000,Engineering
Charlie Wilson,32,Seattle,90000,Product
EOF

# Encrypt the file using your public key (assuming you have GPG installed)
# Replace with your actual public key ID or email
gpg --trust-model always --encrypt --armor --recipient "<EMAIL>" --output sample_data.csv.pgp sample_data.csv

# Or if you have the public key file:
gpg --trust-model always --encrypt --armor --recipient-file /path/to/your/pgp_public_key.asc --output sample_data.csv.pgp sample_data.csv
```

### 4. Test SFTP Connection
```bash
# Test SFTP connection from terminal
sftp ramchandra.kulkarni@*************

# Once connected, navigate to the directory
cd /Users/<USER>/sftp_uploads/Outbound

# List files to verify .pgp files exist
ls -la *.pgp

# Exit SFTP
exit
```

## Test Execution Steps

### 1. Verify Snowflake Setup
Run the verification queries in `test_procedure_local_sftp.sql`:
- Check passphrase table has data
- Verify private key exists in FILE_STRGE_STG/Secrets
- Confirm CSV_FORMAT file format exists

### 2. Place Test Files
Ensure you have .pgp encrypted files in:
```
/Users/<USER>/sftp_uploads/Outbound/
```

### 3. Run the Test Procedure
Execute the main procedure call from `test_procedure_local_sftp.sql`:

```sql
CALL load_pgp_from_sftp(
    '*************',
    22,
    'ramchandra.kulkarni',
    'MayRam@2024',
    '/Users/<USER>/sftp_uploads/Outbound',
    'pgp_private_key.asc',
    'test_pgp_load_table',
    'CSV_FORMAT'
);
```

### 4. Verify Results
- Check if decrypted files appear in FILE_STRGE_STG/Inbound
- Verify data loaded into test_pgp_load_table
- Review any error messages

## Troubleshooting

### Common Issues:
1. **SSH not enabled**: Run `sudo systemsetup -setremotelogin on`
2. **Permission denied**: Check file permissions and user access
3. **No .pgp files found**: Ensure encrypted files exist in Outbound folder
4. **Decryption fails**: Verify passphrase matches the private key
5. **Network issues**: Confirm IP address ************* is correct

### Test Commands:
```bash
# Check if SSH is running
sudo launchctl list | grep ssh

# Test network connectivity
ping *************

# Check if port 22 is open
nc -zv ************* 22

# List files in Outbound directory
ls -la /Users/<USER>/sftp_uploads/Outbound/
```

## Security Notes
- This setup is for testing only
- In production, use proper SSH key authentication
- Consider using a dedicated SFTP user account
- Ensure proper firewall and network security
