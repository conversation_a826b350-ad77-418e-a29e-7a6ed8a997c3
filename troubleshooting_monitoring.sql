-- Troubleshooting and Monitoring Script for PGP SFTP Load Procedure

-- 1. Check procedure existence and definition
SHOW PROCEDURES LIKE 'load_pgp_from_sftp';

-- 2. View procedure definition (updated signature)
SELECT GET_DDL('PROCEDURE', 'load_pgp_from_sftp(STRING, NUMBER, STRING, STRING, STRING, STRING, STRING, STRING)');

-- 3. Check your existing passphrase table
SELECT
    CASE
        WHEN passphrase IS NOT NULL THEN 'Passphrase exists'
        ELSE 'No passphrase'
    END AS passphrase_status,
    LENGTH(passphrase) AS passphrase_length,
    'edw_db.dims.pgp_passphrase_table' AS table_name
FROM edw_db.dims.pgp_passphrase_table
LIMIT 1;

-- 4. List files in your FILE_STRGE_STG folders
SELECT 'FILE_STRGE_STG/Secrets files:' AS folder_info;
LIST @FILE_STRGE_STG/Secrets;

SELECT 'FILE_STRGE_STG/Inbound files:' AS folder_info;
LIST @FILE_STRGE_STG/Inbound;

SELECT 'FILE_STRGE_STG/Outbound files:' AS folder_info;
LIST @FILE_STRGE_STG/Outbound;

-- 5. Check file formats
SHOW FILE FORMATS LIKE '%FORMAT';

-- 6. Recent procedure executions
SELECT 
    start_time,
    end_time,
    total_elapsed_time/1000 AS duration_seconds,
    query_text,
    execution_status,
    error_code,
    error_message
FROM TABLE(INFORMATION_SCHEMA.QUERY_HISTORY())
WHERE query_text ILIKE '%load_pgp_from_sftp%'
    AND start_time >= DATEADD(day, -7, CURRENT_TIMESTAMP())
ORDER BY start_time DESC
LIMIT 20;

-- 7. Check target table structure and recent data
-- Replace 'your_target_table' with your actual table name
-- DESCRIBE TABLE your_target_table;
-- SELECT COUNT(*) AS total_records, MAX(load_timestamp) AS last_load FROM your_target_table;

-- 8. Check for common issues

-- Check if your FILE_STRGE_STG stage exists
SELECT
    stage_name,
    stage_schema,
    stage_type,
    stage_owner,
    comment
FROM INFORMATION_SCHEMA.STAGES
WHERE stage_name = 'FILE_STRGE_STG';

-- Check if file formats exist
SELECT 
    file_format_name,
    file_format_type,
    format_options
FROM INFORMATION_SCHEMA.FILE_FORMATS 
WHERE file_format_name IN ('CSV_FORMAT', 'PIPE_DELIMITED_FORMAT');

-- 9. Test connectivity and permissions
-- Test FILE_STRGE_STG folder access
SELECT 'Testing FILE_STRGE_STG/Secrets access...' AS test;
LIST @FILE_STRGE_STG/Secrets PATTERN='.*';

SELECT 'Testing FILE_STRGE_STG/Inbound access...' AS test;
LIST @FILE_STRGE_STG/Inbound PATTERN='.*';

SELECT 'Testing FILE_STRGE_STG/Outbound access...' AS test;
LIST @FILE_STRGE_STG/Outbound PATTERN='.*';

-- 10. Performance monitoring
SELECT 
    DATE_TRUNC('hour', start_time) AS execution_hour,
    COUNT(*) AS execution_count,
    AVG(total_elapsed_time/1000) AS avg_duration_seconds,
    MAX(total_elapsed_time/1000) AS max_duration_seconds,
    SUM(CASE WHEN execution_status = 'SUCCESS' THEN 1 ELSE 0 END) AS successful_runs,
    SUM(CASE WHEN execution_status != 'SUCCESS' THEN 1 ELSE 0 END) AS failed_runs
FROM TABLE(INFORMATION_SCHEMA.QUERY_HISTORY())
WHERE query_text ILIKE '%load_pgp_from_sftp%'
    AND start_time >= DATEADD(day, -30, CURRENT_TIMESTAMP())
GROUP BY DATE_TRUNC('hour', start_time)
ORDER BY execution_hour DESC
LIMIT 50;

-- 11. Error analysis
SELECT 
    error_code,
    error_message,
    COUNT(*) AS occurrence_count,
    MAX(start_time) AS last_occurrence
FROM TABLE(INFORMATION_SCHEMA.QUERY_HISTORY())
WHERE query_text ILIKE '%load_pgp_from_sftp%'
    AND execution_status != 'SUCCESS'
    AND start_time >= DATEADD(day, -30, CURRENT_TIMESTAMP())
GROUP BY error_code, error_message
ORDER BY occurrence_count DESC;

-- 12. Cleanup old files from FILE_STRGE_STG/Inbound (run periodically)
-- Note: This is a manual process, you may want to create a separate procedure for this
/*
REMOVE @FILE_STRGE_STG/Inbound PATTERN='.*\.csv'
WHERE LAST_MODIFIED < DATEADD(day, -7, CURRENT_TIMESTAMP());
*/

-- 13. Test procedure with your setup (for debugging)
/*
CALL load_pgp_from_sftp(
    'test-host',
    22,
    'test-user',
    'test-pass',
    '/test-dir',
    'pgp_private_key.asc',        -- Your existing private key
    'test_table',
    'CSV_FORMAT'
);
*/

-- 14. Common troubleshooting queries

-- Check Python package availability
SELECT SYSTEM$GET_PYTHON_PACKAGES_INFO();

-- Check warehouse status
SHOW WAREHOUSES;

-- Check current role and privileges
SELECT CURRENT_ROLE(), CURRENT_USER(), CURRENT_DATABASE(), CURRENT_SCHEMA();

-- 15. Maintenance recommendations for your FILE_STRGE_STG setup
SELECT '
MAINTENANCE RECOMMENDATIONS:
1. Regularly clean up old files from FILE_STRGE_STG/Inbound folder
2. Monitor procedure execution times and optimize if needed
3. Rotate PGP passphrases periodically for security
4. Archive or purge old execution logs
5. Monitor FILE_STRGE_STG storage usage across all folders
6. Test procedure with sample data regularly
7. Keep private keys in FILE_STRGE_STG/Secrets secure and backed up
8. Monitor for failed executions and investigate errors
9. Use FILE_STRGE_STG/Outbound for any files you need to send back via SFTP
10. Consider implementing automated cleanup procedures for old files
' AS recommendations;
