-- Diagnos<PERSON> Script for SFTP "Device or resource busy" Error

-- 1. Create debug log table first
CREATE OR REPLACE TABLE debug_log (
    log_level VARCHAR(20),
    message VARCHAR(2000),
    timestamp TIMESTAMP
);

-- 2. Simple socket test to check basic connectivity
CREATE OR REPLACE PROCEDURE test_socket_connection()
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('snowflake-snowpark-python')
HANDLER = 'test_socket_connection'
AS
$$
import socket

def test_socket_connection(session):
    try:
        # Test basic socket connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        result = sock.connect_ex(('us-east-1.sftpcloud.io', 22))
        sock.close()
        
        if result == 0:
            return "SUCCESS: Socket connection to us-east-1.sftpcloud.io:22 successful"
        else:
            return f"ERROR: Socket connection failed with code {result}"
            
    except Exception as e:
        return f"ERROR: Socket test failed - {str(e)}"
$$;

-- 3. Test with different paramiko settings
CREATE OR REPLACE PROCEDURE test_paramiko_minimal()
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('snowflake-snowpark-python', 'paramiko')
HANDLER = 'test_paramiko_minimal'
AS
$$
import paramiko
import time

def test_paramiko_minimal(session):
    transport = None
    try:
        # Minimal paramiko test with explicit settings
        transport = paramiko.Transport(('us-east-1.sftpcloud.io', 22))
        
        # Set explicit timeouts and options
        transport.set_keepalive(30)
        transport.banner_timeout = 30
        transport.auth_timeout = 30
        
        # Try to connect
        transport.connect(
            username='a4877572ac6a4f17916cfd14d7009b6e',
            password='5MnDgP3GNYVGzIPvChqF4ZBfKVdtUgq2'
        )
        
        # Just test if we can authenticate, don't create SFTP client yet
        if transport.is_authenticated():
            return "SUCCESS: Authentication successful"
        else:
            return "ERROR: Authentication failed"
            
    except Exception as e:
        return f"ERROR: {type(e).__name__}: {str(e)}"
        
    finally:
        if transport:
            try:
                transport.close()
            except:
                pass
$$;

-- 4. Test with public SFTP server for comparison
CREATE OR REPLACE PROCEDURE test_public_sftp_detailed()
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('snowflake-snowpark-python', 'paramiko')
HANDLER = 'test_public_sftp_detailed'
AS
$$
import paramiko

def test_public_sftp_detailed(session):
    transport = None
    sftp = None
    try:
        # Test with known working public server
        transport = paramiko.Transport(('test.rebex.net', 22))
        transport.connect(username='demo', password='password')
        
        sftp = paramiko.SFTPClient.from_transport(transport)
        files = sftp.listdir('/pub/example')
        
        return f"SUCCESS: Public SFTP works - found {len(files)} files"
        
    except Exception as e:
        return f"ERROR in public test: {type(e).__name__}: {str(e)}"
        
    finally:
        if sftp:
            try:
                sftp.close()
            except:
                pass
        if transport:
            try:
                transport.close()
            except:
                pass
$$;

-- 5. Run diagnostic tests in sequence
SELECT 'Running Socket Test...' AS test_step;
CALL test_socket_connection();

SELECT 'Running Minimal Paramiko Test...' AS test_step;
CALL test_paramiko_minimal();

SELECT 'Running Public SFTP Test...' AS test_step;
CALL test_public_sftp_detailed();

-- 6. Check all test results
SELECT 'Test Results Summary:' AS summary;
SELECT 
    CASE 
        WHEN query_text ILIKE '%test_socket_connection%' THEN 'Socket Test'
        WHEN query_text ILIKE '%test_paramiko_minimal%' THEN 'Paramiko Auth Test'
        WHEN query_text ILIKE '%test_public_sftp_detailed%' THEN 'Public SFTP Test'
        ELSE 'Other'
    END AS test_type,
    execution_status,
    error_message,
    start_time
FROM TABLE(INFORMATION_SCHEMA.QUERY_HISTORY())
WHERE (query_text ILIKE '%test_socket_connection%' 
       OR query_text ILIKE '%test_paramiko_minimal%' 
       OR query_text ILIKE '%test_public_sftp_detailed%')
    AND start_time >= DATEADD(minute, -5, CURRENT_TIMESTAMP())
ORDER BY start_time DESC;

-- 7. Alternative approach - test with different Python packages
CREATE OR REPLACE PROCEDURE test_with_ftplib()
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('snowflake-snowpark-python')
HANDLER = 'test_with_ftplib'
AS
$$
import ftplib
import socket

def test_with_ftplib(session):
    try:
        # Try FTP instead of SFTP to see if it's a protocol issue
        ftp = ftplib.FTP()
        ftp.connect('us-east-1.sftpcloud.io', 21, timeout=30)
        ftp.login('a4877572ac6a4f17916cfd14d7009b6e', '5MnDgP3GNYVGzIPvChqF4ZBfKVdtUgq2')
        
        files = ftp.nlst('Outbound/')
        ftp.quit()
        
        return f"SUCCESS: FTP connection works - found {len(files)} files"
        
    except Exception as e:
        return f"FTP test result: {type(e).__name__}: {str(e)}"
$$;

-- 8. Test FTP as alternative
SELECT 'Testing FTP Protocol...' AS test_step;
CALL test_with_ftplib();

-- 9. Troubleshooting analysis
SELECT '
DIAGNOSTIC ANALYSIS:

"Device or resource busy" error typically indicates:

1. RESOURCE CONFLICT:
   - Multiple connections to same server
   - Previous connection not properly closed
   - Snowflake resource limitations

2. NETWORK ISSUES:
   - Firewall blocking connection
   - Network timeout
   - DNS resolution problems

3. SERVER-SIDE ISSUES:
   - SFTP server overloaded
   - Connection limits reached
   - Server configuration issues

SOLUTIONS TO TRY:

1. If Socket Test PASSES but Paramiko FAILS:
   - Issue is with SSH/SFTP protocol handling
   - Try different paramiko settings
   - Check if server supports SFTP vs just SSH

2. If Public SFTP Test PASSES but yours FAILS:
   - Issue specific to your SFTP server
   - Check server logs
   - Verify server configuration

3. If ALL tests FAIL:
   - Network connectivity issue from Snowflake
   - Check External Access Integration
   - Verify Network Rules

4. If FTP works but SFTP fails:
   - Server may not support SFTP properly
   - Use FTP protocol instead

IMMEDIATE ACTIONS:
1. Check the test results above
2. Try running tests again after 5 minutes
3. Contact your SFTP provider if issues persist
4. Consider using FTP if SFTP continues to fail

' AS troubleshooting_guide;
