-- Comprehensive SFTP Connection Test Script
-- Tests Network Rule, Secret, and External Access Integration

-- 1. Verify your Snowflake networking components
SELECT 'Checking Network Rules...' AS check_type;
SHOW NETWORK RULES;

SELECT 'Checking Secrets...' AS check_type;
SHOW SECRETS;

SELECT 'Checking External Access Integrations...' AS check_type;
SHOW INTEGRATIONS;

-- 2. Create a simple SFTP connection test procedure
CREATE OR REPLACE PROCEDURE test_sftp_connection(
    sftp_host STRING,
    sftp_port INTEGER,
    sftp_user STRING,
    sftp_password STRING,
    sftp_dir STRING
)
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('paramiko')
HANDLER = 'test_sftp_connection'
AS
$$
import paramiko
import logging

def test_sftp_connection(session, sftp_host, sftp_port, sftp_user, sftp_password, sftp_dir):
    """
    Simple SFTP connection test with detailed logging
    """
    try:
        # Enable detailed logging
        paramiko.util.log_to_file('/tmp/paramiko.log', level=logging.DEBUG)
        
        # Test 1: Create transport
        session.sql("INSERT INTO sftp_test_log VALUES ('INFO', 'Starting SFTP connection test', CURRENT_TIMESTAMP())").collect()
        
        transport = paramiko.Transport((sftp_host, sftp_port))
        session.sql(f"INSERT INTO sftp_test_log VALUES ('INFO', 'Transport created for {sftp_host}:{sftp_port}', CURRENT_TIMESTAMP())").collect()
        
        # Test 2: Connect with authentication
        transport.connect(username=sftp_user, password=sftp_password)
        session.sql(f"INSERT INTO sftp_test_log VALUES ('SUCCESS', 'Authentication successful for user {sftp_user}', CURRENT_TIMESTAMP())").collect()
        
        # Test 3: Create SFTP client
        sftp = paramiko.SFTPClient.from_transport(transport)
        session.sql("INSERT INTO sftp_test_log VALUES ('SUCCESS', 'SFTP client created successfully', CURRENT_TIMESTAMP())").collect()
        
        # Test 4: List directory contents
        try:
            files = sftp.listdir(sftp_dir)
            file_count = len(files)
            session.sql(f"INSERT INTO sftp_test_log VALUES ('SUCCESS', 'Directory listing successful. Found {file_count} files in {sftp_dir}', CURRENT_TIMESTAMP())").collect()
            
            # Log first few files
            for i, file in enumerate(files[:5]):
                session.sql(f"INSERT INTO sftp_test_log VALUES ('INFO', 'File {i+1}: {file}', CURRENT_TIMESTAMP())").collect()
                
        except Exception as dir_error:
            session.sql(f"INSERT INTO sftp_test_log VALUES ('ERROR', 'Directory listing failed: {str(dir_error)}', CURRENT_TIMESTAMP())").collect()
            return f"Directory access failed: {str(dir_error)}"
        
        # Test 5: Check for .pgp files specifically
        pgp_files = [f for f in files if f.endswith('.pgp')]
        session.sql(f"INSERT INTO sftp_test_log VALUES ('INFO', 'Found {len(pgp_files)} PGP files', CURRENT_TIMESTAMP())").collect()
        
        # Clean up
        sftp.close()
        transport.close()
        session.sql("INSERT INTO sftp_test_log VALUES ('SUCCESS', 'SFTP connection test completed successfully', CURRENT_TIMESTAMP())").collect()
        
        return f"SUCCESS: Connected to {sftp_host}:{sftp_port}, found {file_count} files, {len(pgp_files)} PGP files in {sftp_dir}"
        
    except paramiko.AuthenticationException as auth_error:
        session.sql(f"INSERT INTO sftp_test_log VALUES ('ERROR', 'Authentication failed: {str(auth_error)}', CURRENT_TIMESTAMP())").collect()
        return f"Authentication failed: {str(auth_error)}"
        
    except paramiko.SSHException as ssh_error:
        session.sql(f"INSERT INTO sftp_test_log VALUES ('ERROR', 'SSH error: {str(ssh_error)}', CURRENT_TIMESTAMP())").collect()
        return f"SSH error: {str(ssh_error)}"
        
    except Exception as e:
        session.sql(f"INSERT INTO sftp_test_log VALUES ('ERROR', 'Connection failed: {str(e)}', CURRENT_TIMESTAMP())").collect()
        return f"Connection failed: {str(e)}"
$$;

-- 3. Create logging table for detailed test results
CREATE OR REPLACE TABLE sftp_test_log (
    log_level VARCHAR(20),
    message VARCHAR(2000),
    timestamp TIMESTAMP
);

-- 4. Test your SFTP connection (update with your actual details)
-- Replace these values with your actual SFTP server details
CALL test_sftp_connection(
    '*************',                  -- Your SFTP host
    22,                               -- SFTP port
    'ramchandra.kulkarni',            -- Username
    'MayRam@2024',                    -- Password
    '/Users/<USER>/sftp_uploads/Outbound'  -- Directory path
);

-- 5. Check detailed test results
SELECT 'SFTP Connection Test Results:' AS results;
SELECT * FROM sftp_test_log ORDER BY timestamp DESC;

-- 6. Check for specific error patterns
SELECT 'Error Analysis:' AS analysis;
SELECT 
    log_level,
    COUNT(*) AS count,
    LISTAGG(DISTINCT message, '; ') AS messages
FROM sftp_test_log 
WHERE log_level = 'ERROR'
GROUP BY log_level;

-- 7. Network connectivity test
SELECT 'Network Connectivity Check:' AS network_check;
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'Network rules and integrations are configured'
        ELSE 'No network rules found - this may be the issue'
    END AS network_status
FROM INFORMATION_SCHEMA.NETWORK_RULES;

-- 8. Test with alternative SFTP servers for comparison
-- Test with public SFTP server to verify your setup works
/*
CALL test_sftp_connection(
    'test.rebex.net',
    22,
    'demo',
    'password',
    '/pub/example'
);
*/

-- 9. Detailed procedure execution history
SELECT 'Procedure Execution History:' AS history;
SELECT 
    start_time,
    end_time,
    execution_status,
    error_code,
    error_message,
    total_elapsed_time/1000 AS duration_seconds
FROM TABLE(INFORMATION_SCHEMA.QUERY_HISTORY())
WHERE query_text ILIKE '%test_sftp_connection%'
    AND start_time >= DATEADD(hour, -2, CURRENT_TIMESTAMP())
ORDER BY start_time DESC;

-- 10. Check your network rule details
SELECT 'Network Rule Details:' AS rule_details;
DESCRIBE NETWORK RULE your_network_rule_name;  -- Replace with your actual network rule name

-- 11. Check your secret details
SELECT 'Secret Details:' AS secret_details;
DESCRIBE SECRET your_secret_name;  -- Replace with your actual secret name

-- 12. Check your external access integration
SELECT 'External Access Integration Details:' AS integration_details;
DESCRIBE INTEGRATION your_integration_name;  -- Replace with your actual integration name

-- 13. Clean up test data (run after testing)
/*
DROP TABLE sftp_test_log;
DROP PROCEDURE test_sftp_connection(STRING, INTEGER, STRING, STRING, STRING);
*/

-- 14. Common troubleshooting queries
SELECT '
TROUBLESHOOTING CHECKLIST:

1. NETWORK RULE: Should allow your SFTP host and port
   - Check: SHOW NETWORK RULES;
   - Verify: Host and port are included in the rule

2. SECRET: Should contain SFTP credentials
   - Check: SHOW SECRETS;
   - Verify: Secret contains username/password

3. EXTERNAL ACCESS INTEGRATION: Should reference network rule and secret
   - Check: SHOW INTEGRATIONS;
   - Verify: Integration is properly configured

4. PROCEDURE GRANTS: Should have access to integration
   - Check: Procedure has USAGE on integration

5. NETWORK CONNECTIVITY: Snowflake should reach your SFTP server
   - Check: SFTP server is accessible from internet
   - Verify: Firewall/router allows connections

COMMON ERRORS:
- "Network is unreachable": SFTP server not accessible from internet
- "Authentication failed": Wrong username/password in secret
- "Permission denied": Network rule not allowing connection
- "Connection refused": SFTP service not running or wrong port

' AS troubleshooting_guide;
