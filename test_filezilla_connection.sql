-- Test Script for FileZilla SFTP Server Connection

-- 1. Test with FileZilla Server on your Mac
CALL load_pgp_from_sftp(
    '*************',                  -- Your Mac IP
    21,                               -- FTP port (FileZilla default)
    'snowflake_test',                 -- FileZilla user you created
    'SnowTest2024!',                  -- FileZilla password
    '/Outbound',                      -- Relative path in FileZilla (maps to your Outbound folder)
    'pgp_private_key.asc',
    'BOOKINGS_D',
    'CSV_FORMAT'
);

-- 2. Alternative: Test with SFTP port if FileZilla supports it
/*
CALL load_pgp_from_sftp(
    '*************',
    22,                               -- SFTP port
    'snowflake_test',
    'SnowTest2024!',
    '/Outbound',
    'pgp_private_key.asc',
    'BOOKINGS_D',
    'CSV_FORMAT'
);
*/

-- 3. If local IP still doesn't work, try with your public IP (after port forwarding)
/*
-- First find your public IP: curl ifconfig.me
-- Then set up port forwarding on your router: External 2222 -> Internal *************:21

CALL load_pgp_from_sftp(
    'YOUR_PUBLIC_IP_HERE',            -- Replace with your actual public IP
    2222,                             -- Forwarded port
    'snowflake_test',
    'SnowTest2024!',
    '/Outbound',
    'pgp_private_key.asc',
    'BOOKINGS_D',
    'CSV_FORMAT'
);
*/

-- 4. Quick test with public SFTP server (for immediate testing)
/*
-- Upload your .pgp files to test.rebex.net first using FileZilla Client
CALL load_pgp_from_sftp(
    'test.rebex.net',
    22,
    'demo',
    'password',
    '/pub/example',                   -- Upload your .pgp files here first
    'pgp_private_key.asc',
    'BOOKINGS_D',
    'CSV_FORMAT'
);
*/

-- 5. Verification queries after running the procedure
SELECT 'Checking procedure execution results...' AS status;

-- Check if files were processed
LIST @FILE_STRGE_STG/Inbound;

-- Check if data was loaded
SELECT COUNT(*) AS records_loaded FROM BOOKINGS_D 
WHERE load_timestamp >= DATEADD(minute, -10, CURRENT_TIMESTAMP());

-- View recent data
SELECT * FROM BOOKINGS_D 
WHERE load_timestamp >= DATEADD(minute, -10, CURRENT_TIMESTAMP())
LIMIT 10;

-- Check for any errors in recent executions
SELECT 
    start_time,
    execution_status,
    error_message,
    total_elapsed_time/1000 AS duration_seconds
FROM TABLE(INFORMATION_SCHEMA.QUERY_HISTORY())
WHERE query_text ILIKE '%load_pgp_from_sftp%'
    AND start_time >= DATEADD(hour, -1, CURRENT_TIMESTAMP())
ORDER BY start_time DESC
LIMIT 5;
