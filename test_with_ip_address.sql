-- Test SFTP Connection Using IP Address Instead of Hostname

-- First, let's find the IP address of us-east-1.sftpcloud.io
-- Run this on your local machine to get the IP:
-- nslookup us-east-1.sftpcloud.io
-- or: dig us-east-1.sftpcloud.io

-- Common SftpCloud IP addresses (you'll need to verify the correct one):
-- Try these IPs one by one

-- Test 1: Using a common SftpCloud IP (replace with actual IP)
CREATE OR REPLACE PROCEDURE test_sftp_with_ip()
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('snowflake-snowpark-python', 'paramiko')
HANDLER = 'test_sftp_with_ip'
AS
$$
import paramiko
import socket

def test_sftp_with_ip(session):
    # Common SftpCloud IPs - try these one by one
    # You'll need to find the correct IP for us-east-1.sftpcloud.io
    test_ips = [
        '************',    # Example IP - replace with actual
        '*************',   # Example IP - replace with actual
        '************'     # Example IP - replace with actual
    ]
    
    for ip in test_ips:
        try:
            # Test socket connection first
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((ip, 22))
            sock.close()
            
            if result == 0:
                # Socket works, try SFTP
                transport = paramiko.Transport((ip, 22))
                transport.connect(
                    username='a4877572ac6a4f17916cfd14d7009b6e',
                    password='5MnDgP3GNYVGzIPvChqF4ZBfKVdtUgq2',
                    timeout=30
                )
                
                sftp = paramiko.SFTPClient.from_transport(transport)
                files = sftp.listdir('Outbound/')
                
                sftp.close()
                transport.close()
                
                return f"SUCCESS: Connected to {ip} and found {len(files)} files"
            else:
                continue
                
        except Exception as e:
            continue
    
    return "ERROR: Could not connect to any IP address"
$$;

-- Test with IP address
CALL test_sftp_with_ip();

-- Alternative: Create a simple network test procedure
CREATE OR REPLACE PROCEDURE test_network_access()
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('snowflake-snowpark-python')
HANDLER = 'test_network_access'
AS
$$
import socket

def test_network_access(session):
    results = []
    
    # Test various hostnames to see what works
    test_hosts = [
        ('google.com', 80),
        ('test.rebex.net', 22),
        ('us-east-1.sftpcloud.io', 22),
        ('*******', 53)  # Google DNS
    ]
    
    for host, port in test_hosts:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                results.append(f"SUCCESS: {host}:{port}")
            else:
                results.append(f"FAILED: {host}:{port} (code {result})")
                
        except Exception as e:
            results.append(f"ERROR: {host}:{port} - {str(e)}")
    
    return " | ".join(results)
$$;

-- Test network access to various hosts
CALL test_network_access();

-- Check your current Snowflake network configuration
SELECT 'Current Network Rules:' AS config_check;
SHOW NETWORK RULES;

SELECT 'Current Secrets:' AS config_check;
SHOW SECRETS;

SELECT 'Current External Access Integrations:' AS config_check;
SHOW INTEGRATIONS;

-- Template for fixing your network rule (update with your actual names)
/*
-- Example of what your network rule should look like:
CREATE OR REPLACE NETWORK RULE your_sftp_network_rule
  MODE = EGRESS
  TYPE = HOST_PORT
  VALUE_LIST = ('us-east-1.sftpcloud.io:22');

-- Update your external access integration
CREATE OR REPLACE EXTERNAL ACCESS INTEGRATION your_sftp_integration
  ALLOWED_NETWORK_RULES = (your_sftp_network_rule)
  ENABLED = true;

-- Grant usage to your role
GRANT USAGE ON INTEGRATION your_sftp_integration TO ROLE your_role;
*/

-- Instructions for finding the correct IP address
SELECT '
TO FIND THE CORRECT IP ADDRESS:

1. On your local machine, run:
   nslookup us-east-1.sftpcloud.io
   
2. Or use:
   dig us-east-1.sftpcloud.io
   
3. Or use online tools:
   - whatismyipaddress.com/hostname-ip
   - dnschecker.org

4. Update your Network Rule with the IP address:
   CREATE OR REPLACE NETWORK RULE sftp_rule
   MODE = EGRESS
   TYPE = HOST_PORT
   VALUE_LIST = (''IP_ADDRESS:22'');

5. Or better yet, use hostname in your network rule:
   CREATE OR REPLACE NETWORK RULE sftp_rule
   MODE = EGRESS
   TYPE = HOST_PORT
   VALUE_LIST = (''us-east-1.sftpcloud.io:22'');

NEXT STEPS:
1. Check your current network rules above
2. Verify they include us-east-1.sftpcloud.io:22
3. If not, update them
4. Test again with the corrected network rule

' AS instructions;
