-- Setup and Deployment Script for PGP SFTP Load Procedure
-- Updated for your existing FILE_STRGE_STG infrastructure
-- Run this script to verify and set up any missing components

-- 1. Set the context
USE DATABASE edw_db;
USE SCHEMA dims;

-- 2. Verify your existing passphrase table
SELECT 'Checking existing pgp_passphrase_table...' AS status;
DESCRIBE TABLE edw_db.dims.pgp_passphrase_table;
SELECT COUNT(*) AS passphrase_count FROM edw_db.dims.pgp_passphrase_table;

-- 3. Verify your existing FILE_STRGE_STG stage and folder structure
SELECT 'Checking existing FILE_STRGE_STG stage...' AS status;
SHOW STAGES LIKE 'FILE_STRGE_STG';

-- List contents of your existing folders
SELECT 'Listing Secrets folder contents...' AS status;
LIST @FILE_STRGE_STG/Secrets;

SELECT 'Listing Inbound folder contents...' AS status;
LIST @FILE_STRGE_STG/Inbound;

SELECT 'Listing Outbound folder contents...' AS status;
LIST @FILE_STRGE_STG/Outbound;

-- 4. Create file formats for different types of files
CREATE OR REPLACE FILE FORMAT CSV_FORMAT
    TYPE = 'CSV'
    FIELD_DELIMITER = ','
    RECORD_DELIMITER = '\n'
    SKIP_HEADER = 1
    FIELD_OPTIONALLY_ENCLOSED_BY = '"'
    TRIM_SPACE = TRUE
    ERROR_ON_COLUMN_COUNT_MISMATCH = FALSE
    ESCAPE = 'NONE'
    ESCAPE_UNENCLOSED_FIELD = '\134'
    DATE_FORMAT = 'AUTO'
    TIMESTAMP_FORMAT = 'AUTO'
    NULL_IF = ('\\N', 'NULL', 'null', '')
    COMMENT = 'CSV format for loading decrypted PGP files';

CREATE OR REPLACE FILE FORMAT PIPE_DELIMITED_FORMAT
    TYPE = 'CSV'
    FIELD_DELIMITER = '|'
    RECORD_DELIMITER = '\n'
    SKIP_HEADER = 1
    FIELD_OPTIONALLY_ENCLOSED_BY = '"'
    TRIM_SPACE = TRUE
    ERROR_ON_COLUMN_COUNT_MISMATCH = FALSE
    ESCAPE = 'NONE'
    DATE_FORMAT = 'AUTO'
    TIMESTAMP_FORMAT = 'AUTO'
    NULL_IF = ('\\N', 'NULL', 'null', '')
    COMMENT = 'Pipe-delimited format for loading decrypted PGP files';

-- 5. Create a sample target table (modify as needed for your data structure)
CREATE TABLE IF NOT EXISTS sample_target_table (
    record_id NUMBER AUTOINCREMENT PRIMARY KEY,
    column1 VARCHAR(100),
    column2 VARCHAR(100),
    column3 NUMBER,
    column4 DATE,
    column5 VARCHAR(500),
    file_name VARCHAR(255),
    load_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
    batch_id VARCHAR(50)
);

-- 6. Create a logging table for procedure execution tracking
CREATE TABLE IF NOT EXISTS pgp_load_log (
    log_id NUMBER AUTOINCREMENT PRIMARY KEY,
    execution_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
    sftp_host VARCHAR(255),
    sftp_dir VARCHAR(500),
    files_processed NUMBER,
    status VARCHAR(50),
    message TEXT,
    target_table VARCHAR(255),
    execution_time_seconds NUMBER
);

-- 7. Grant necessary permissions for FILE_STRGE_STG (adjust roles as needed)
-- GRANT USAGE ON STAGE FILE_STRGE_STG TO ROLE your_role;
-- GRANT READ ON STAGE FILE_STRGE_STG TO ROLE your_role;
-- GRANT WRITE ON STAGE FILE_STRGE_STG TO ROLE your_role;
-- GRANT SELECT ON TABLE edw_db.dims.pgp_passphrase_table TO ROLE your_role;
-- GRANT INSERT ON TABLE sample_target_table TO ROLE your_role;
-- GRANT INSERT ON TABLE pgp_load_log TO ROLE your_role;

-- 8. Verify your existing passphrase (DO NOT run if passphrase already exists)
-- Your passphrase should already be in edw_db.dims.pgp_passphrase_table
-- If you need to update it, use:
-- UPDATE edw_db.dims.pgp_passphrase_table
-- SET passphrase = 'your_actual_passphrase_here'
-- WHERE id = (SELECT MIN(id) FROM edw_db.dims.pgp_passphrase_table);

-- 9. Verification queries for your existing setup
SELECT 'Passphrase table verification' AS status, COUNT(*) AS record_count
FROM edw_db.dims.pgp_passphrase_table;

SELECT 'FILE_STRGE_STG stage verification' AS status;
SHOW STAGES LIKE 'FILE_STRGE_STG';

SELECT 'File formats verification' AS status;
SHOW FILE FORMATS LIKE '%FORMAT';

SELECT 'Tables verification' AS status;
SHOW TABLES LIKE '%target_table' OR LIKE '%log';

-- Verify your PGP keys exist in Secrets folder
SELECT 'PGP Keys verification' AS status;
LIST @FILE_STRGE_STG/Secrets PATTERN='.*pgp.*key.*';

-- 10. Instructions for your existing setup
SELECT '
SETUP VERIFICATION CHECKLIST:
✓ FILE_STRGE_STG stage exists with Secrets, Inbound, Outbound folders
✓ pgp_private_key.asc and pgp_public_key.asc exist in FILE_STRGE_STG/Secrets
✓ edw_db.dims.pgp_passphrase_table exists and contains your passphrase

DEPLOYMENT STEPS:
1. Deploy the stored procedure by running SnowProc.sql
2. Test the procedure with sample_usage.sql
3. Adjust target table structure based on your actual data format
4. Set up monitoring using troubleshooting_monitoring.sql

OPTIONAL STEPS:
- Create additional file formats if needed for different data types
- Set up automated scheduling for regular SFTP processing
- Configure alerts for failed procedure executions
' AS instructions;
