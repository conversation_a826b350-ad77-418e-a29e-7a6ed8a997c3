-- Verification Script for Your Existing FILE_STRGE_STG Setup
-- Run this script to verify all components are ready for the PGP SFTP procedure

-- 1. Check database and schema context
SELECT CURRENT_DATABASE() AS current_db, CURRENT_SCHEMA() AS current_schema;

-- 2. Verify FILE_STRGE_STG stage exists
SELECT 'FILE_STRGE_STG Stage Verification' AS check_type;
SHOW STAGES LIKE 'FILE_STRGE_STG';

-- 3. Verify folder structure in FILE_STRGE_STG
SELECT 'Checking Secrets folder...' AS folder_check;
LIST @FILE_STRGE_STG/Secrets;

SELECT 'Checking Inbound folder...' AS folder_check;
LIST @FILE_STRGE_STG/Inbound;

SELECT 'Checking Outbound folder...' AS folder_check;
LIST @FILE_STRGE_STG/Outbound;

-- 4. Verify PGP keys exist in Secrets folder
SELECT 'PGP Keys Verification' AS check_type;
LIST @FILE_STRGE_STG/Secrets PATTERN='.*pgp_private_key.asc.*';
LIST @FILE_STRGE_STG/Secrets PATTERN='.*pgp_public_key.asc.*';

-- 5. Verify passphrase table exists and has data
SELECT 'Passphrase Table Verification' AS check_type;
SELECT 
    COUNT(*) AS total_records,
    COUNT(CASE WHEN passphrase IS NOT NULL THEN 1 END) AS records_with_passphrase,
    MAX(LENGTH(passphrase)) AS max_passphrase_length
FROM edw_db.dims.pgp_passphrase_table;

-- 6. Check table structure
DESCRIBE TABLE edw_db.dims.pgp_passphrase_table;

-- 7. Verify file formats exist (create if missing)
SELECT 'File Formats Check' AS check_type;
SHOW FILE FORMATS LIKE '%CSV%';
SHOW FILE FORMATS LIKE '%PIPE%';

-- Create basic file formats if they don't exist
CREATE FILE FORMAT IF NOT EXISTS CSV_FORMAT
    TYPE = 'CSV'
    FIELD_DELIMITER = ','
    RECORD_DELIMITER = '\n'
    SKIP_HEADER = 1
    FIELD_OPTIONALLY_ENCLOSED_BY = '"'
    TRIM_SPACE = TRUE
    ERROR_ON_COLUMN_COUNT_MISMATCH = FALSE
    NULL_IF = ('\\N', 'NULL', 'null', '')
    COMMENT = 'CSV format for PGP decrypted files';

CREATE FILE FORMAT IF NOT EXISTS PIPE_DELIMITED_FORMAT
    TYPE = 'CSV'
    FIELD_DELIMITER = '|'
    RECORD_DELIMITER = '\n'
    SKIP_HEADER = 1
    FIELD_OPTIONALLY_ENCLOSED_BY = '"'
    TRIM_SPACE = TRUE
    ERROR_ON_COLUMN_COUNT_MISMATCH = FALSE
    NULL_IF = ('\\N', 'NULL', 'null', '')
    COMMENT = 'Pipe-delimited format for PGP decrypted files';

-- 8. Check permissions (current role capabilities)
SELECT 'Permission Check' AS check_type;
SELECT CURRENT_ROLE() AS current_role, CURRENT_USER() AS current_user;

-- 9. Test stage access
SELECT 'Testing Stage Access' AS check_type;
-- This will show if you can access the stage
LIST @FILE_STRGE_STG PATTERN='.*' LIMIT 5;

-- 10. Summary report
SELECT 
    'SETUP VERIFICATION SUMMARY' AS report_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STAGES WHERE STAGE_NAME = 'FILE_STRGE_STG') > 0 
        THEN '✓ FILE_STRGE_STG stage exists'
        ELSE '✗ FILE_STRGE_STG stage missing'
    END AS stage_status,
    CASE 
        WHEN (SELECT COUNT(*) FROM edw_db.dims.pgp_passphrase_table WHERE passphrase IS NOT NULL) > 0 
        THEN '✓ Passphrase table has data'
        ELSE '✗ Passphrase table empty or missing'
    END AS passphrase_status,
    CASE 
        WHEN (SELECT COUNT(*) FROM INFORMATION_SCHEMA.FILE_FORMATS WHERE FILE_FORMAT_NAME IN ('CSV_FORMAT', 'PIPE_DELIMITED_FORMAT')) >= 2
        THEN '✓ File formats ready'
        ELSE '✓ File formats created'
    END AS format_status;

-- 11. Next steps
SELECT '
VERIFICATION COMPLETE!

NEXT STEPS:
1. If all checks passed, deploy the procedure: Run SnowProc.sql
2. Test the procedure: Use sample_usage.sql
3. Monitor execution: Use troubleshooting_monitoring.sql

READY TO USE:
- Stage: FILE_STRGE_STG with Secrets, Inbound, Outbound folders
- Keys: pgp_private_key.asc and pgp_public_key.asc in Secrets folder
- Passphrase: edw_db.dims.pgp_passphrase_table
- File formats: CSV_FORMAT and PIPE_DELIMITED_FORMAT

PROCEDURE SIGNATURE:
CALL load_pgp_from_sftp(
    sftp_host,
    sftp_port,
    sftp_user,
    sftp_password,
    sftp_dir,
    private_key_file,    -- Required: e.g., 'pgp_private_key.asc'
    target_table,
    file_format
);
' AS next_steps;
