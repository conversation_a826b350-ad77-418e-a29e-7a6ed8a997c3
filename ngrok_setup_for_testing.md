# Quick Fix: Using ngrok for SFTP Testing

## The Problem
Your Mac (*************) is on a private network and not accessible from Snowflake's cloud infrastructure. <PERSON><PERSON><PERSON> cannot reach your local SFTP server.

## The Solution: ngrok Tunnel

### Step 1: Install ngrok
```bash
# Install ngrok using Homebrew
brew install ngrok

# Or download from https://ngrok.com/download
```

### Step 2: Create SSH Tunnel
```bash
# Start ngrok tunnel for SSH/SFTP (port 22)
ngrok tcp 22
```

You'll see output like this:
```
ngrok by @inconshreveable

Session Status                online
Account                       your-account
Version                       3.x.x
Region                        United States (us)
Forwarding                    tcp://0.tcp.ngrok.io:12345 -> localhost:22
```

### Step 3: Note the Connection Details
From the ngrok output, note:
- **Hostname**: `0.tcp.ngrok.io` (or similar)
- **Port**: `12345` (or whatever port ngrok assigns)

### Step 4: Update Your Procedure Call
```sql
CALL load_pgp_from_sftp(
    '0.tcp.ngrok.io',                 -- ngrok hostname (replace with your actual)
    12345,                            -- ngrok port (replace with your actual)
    'ramchandra.kulkarni',            -- Your Mac username
    'MayRam@2024',                    -- Your Mac password
    '/Users/<USER>/sftp_uploads/Outbound',
    'pgp_private_key.asc',
    'BOOKINGS_D',
    'CSV_FORMAT'
);
```

### Step 5: Test the Connection
Before running the Snowflake procedure, test the ngrok tunnel:
```bash
# Test SFTP connection through ngrok
sftp -P 12345 <EMAIL>
```

## Alternative Solutions

### Option 1: Public SFTP Test Server
Use a public test server instead of your local Mac:

```sql
-- Using test.rebex.net (public test server)
CALL load_pgp_from_sftp(
    'test.rebex.net',
    22,
    'demo',
    'password',
    '/pub/example',                   -- Put your .pgp files here first
    'pgp_private_key.asc',
    'BOOKINGS_D',
    'CSV_FORMAT'
);
```

### Option 2: Router Port Forwarding
1. Log into your router's admin panel
2. Set up port forwarding: External Port 2222 → Internal IP *************:22
3. Find your public IP: `curl ifconfig.me`
4. Use your public IP and port 2222 in the procedure call

### Option 3: Cloud SFTP Server
Set up a temporary SFTP server on:
- AWS EC2 with SFTP enabled
- Google Cloud Compute Engine
- Azure Virtual Machine
- DigitalOcean Droplet

## Recommended Approach for Testing

**Use ngrok** - it's the quickest and easiest solution for testing:

1. **Terminal 1**: Run `ngrok tcp 22`
2. **Terminal 2**: Test connection with `sftp -P [ngrok-port] ramchandra.kulkarni@[ngrok-host]`
3. **Snowflake**: Run procedure with ngrok details

## Security Notes
- ngrok creates a temporary public tunnel to your Mac
- Only use for testing purposes
- Stop ngrok when testing is complete: `Ctrl+C`
- For production, use proper cloud-based SFTP servers

## Troubleshooting ngrok
```bash
# Check if SSH is running on your Mac
sudo launchctl list | grep ssh

# Enable SSH if not running
sudo systemsetup -setremotelogin on

# Test local SSH connection first
ssh ramchandra.kulkarni@localhost

# Check ngrok status
ngrok status
```
