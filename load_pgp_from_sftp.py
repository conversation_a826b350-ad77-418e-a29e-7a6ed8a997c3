import os
import paramiko
import gnupg
import tempfile
import shutil
from snowflake.snowpark import Session
from snowflake.snowpark.functions import sproc

@sproc(name="load_pgp_from_sftp", replace=True, is_permanent=True, stage_location="@Inbound", packages=["paramiko", "python-gnupg"])
def load_pgp_from_sftp(
    sftp_host: str,
    sftp_port: int,
    sftp_user: str,
    sftp_password: str,
    sftp_dir: str,
    secrets_stage: str,           # e.g., 'Secrets'
    private_key_file: str,        # e.g., 'private.key'
    passphrase_table: str,        # e.g., 'edw_db.dims.pgp_passphrase_table'
    inbound_stage: str,           # e.g., 'Inbound'
    target_table: str,            # e.g., 'my_table'
    file_format: str,             # e.g., 'my_csv_format'
    session: Session
) -> str:
    """
    Downloads PGP files from SFTP, decrypts, uploads to Inbound stage, loads into table.
    All secrets are managed via Snowflake stages/tables.
    """
    temp_dir = tempfile.mkdtemp()
    try:
        # 1. Get passphrase from table
        passphrase_row = session.sql(f"SELECT passphrase FROM {passphrase_table} LIMIT 1").collect()
        if not passphrase_row:
            raise Exception("No passphrase found in table.")
        pgp_passphrase = passphrase_row[0][0]

        # 2. Download PGP private key from Secrets stage
        private_key_stage_path = f"@{secrets_stage}/{private_key_file}"
        private_key_local = os.path.join(temp_dir, private_key_file)
        session.file.get(private_key_stage_path, private_key_local)

        # 3. Connect to SFTP and download .pgp files
        transport = paramiko.Transport((sftp_host, sftp_port))
        transport.connect(username=sftp_user, password=sftp_password)
        sftp = paramiko.SFTPClient.from_transport(transport)
        files = sftp.listdir(sftp_dir)
        downloaded_files = []
        for file in files:
            if file.endswith('.pgp'):
                local_path = os.path.join(temp_dir, file)
                sftp.get(os.path.join(sftp_dir, file), local_path)
                downloaded_files.append(local_path)
        sftp.close()
        transport.close()
        if not downloaded_files:
            return "No PGP files found in SFTP directory."

        # 4. Decrypt files
        gpg = gnupg.GPG()
        with open(private_key_local, 'r') as f:
            key_data = f.read()
        gpg.import_keys(key_data)
        decrypted_files = []
        for encrypted_path in downloaded_files:
            decrypted_path = encrypted_path.replace('.pgp', '.csv')
            with open(encrypted_path, 'rb') as f:
                status = gpg.decrypt_file(f, passphrase=pgp_passphrase, output=decrypted_path)
            if status.ok:
                decrypted_files.append(decrypted_path)
            else:
                raise Exception(f"Decryption failed for {encrypted_path}: {status.status}")

        # 5. Upload decrypted files to Inbound stage
        for decrypted_file in decrypted_files:
            session.file.put(decrypted_file, f"@{inbound_stage}", overwrite=True)

        # 6. Load into table
        for decrypted_file in decrypted_files:
            file_name = os.path.basename(decrypted_file)
            session.sql(f"""
                COPY INTO {target_table}
                FROM @{inbound_stage}/{file_name}
                FILE_FORMAT = (FORMAT_NAME = '{file_format}')
                ON_ERROR = 'CONTINUE'
            """).collect()

        return f"Loaded {len(decrypted_files)} files into {target_table}."

    finally:
        shutil.rmtree(temp_dir)