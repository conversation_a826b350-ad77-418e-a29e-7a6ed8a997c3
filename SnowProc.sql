CREATE OR <PERSON><PERSON>LACE PROCEDURE load_pgp_from_sftp(
    sftp_host STRING,
    sftp_port INTEGER,
    sftp_user STRING,
    sftp_password STRING,
    sftp_dir STRING,
    private_key_file STRING,
    target_table STRING,
    file_format STRING
)
R<PERSON><PERSON>NS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('paramiko', 'python-gnupg', 'snowflake-snowpark-python')
HANDLER = 'load_pgp_from_sftp'
AS
$$
import os
import paramiko
import gnupg
import tempfile
import shutil

def load_pgp_from_sftp(session, sftp_host, sftp_port, sftp_user, sftp_password, sftp_dir, private_key_file, target_table, file_format):
    """
    Downloads PGP files from SFTP, decrypts them using keys from FILE_STRGE_STG/Secrets,
    and loads the decrypted data into Snowflake tables via FILE_STRGE_STG/Inbound.

    Uses existing infrastructure:
    - Stage: FILE_STRGE_STG with Secrets, Inbound, and Outbound folders
    - Private key: FILE_STRGE_STG/Secrets/pgp_private_key.asc (or custom file)
    - Passphrase table: edw_db.dims.pgp_passphrase_table

    Args:
        session: Snowflake session object
        sftp_host: SFTP server hostname
        sftp_port: SFTP server port
        sftp_user: SFTP username
        sftp_password: SFTP password
        sftp_dir: Directory on SFTP server containing PGP files
        private_key_file: Name of the private key file in FILE_STRGE_STG/Secrets (e.g., 'pgp_private_key.asc')
        target_table: Target table for loading data
        file_format: Snowflake file format name for COPY INTO

    Returns:
        String message indicating success or failure
    """
    temp_dir = tempfile.mkdtemp()
    try:
        # 1. Get passphrase from edw_db.dims.pgp_passphrase_table
        try:
            passphrase_query = "SELECT passphrase FROM edw_db.dims.pgp_passphrase_table WHERE passphrase IS NOT NULL LIMIT 1"
            passphrase_result = session.sql(passphrase_query).collect()
            if not passphrase_result or len(passphrase_result) == 0:
                return "ERROR: No passphrase found in edw_db.dims.pgp_passphrase_table"
            pgp_passphrase = passphrase_result[0][0]
            if not pgp_passphrase:
                return "ERROR: Passphrase is NULL in edw_db.dims.pgp_passphrase_table"
        except Exception as e:
            return f"ERROR: Failed to retrieve passphrase from edw_db.dims.pgp_passphrase_table: {str(e)}"

        # 2. Download PGP private key from FILE_STRGE_STG/Secrets folder
        try:
            private_key_stage_path = f"@FILE_STRGE_STG/Secrets/{private_key_file}"
            private_key_local = os.path.join(temp_dir, private_key_file)
            get_result = session.file.get(private_key_stage_path, temp_dir)
            if not os.path.exists(private_key_local):
                return f"ERROR: Failed to download private key from {private_key_stage_path}"
        except Exception as e:
            return f"ERROR: Failed to download private key: {str(e)}"

        # 3. Connect to SFTP and download .pgp files with improved error handling
        transport = None
        sftp = None
        try:
            transport = paramiko.Transport((sftp_host, sftp_port))
            transport.connect(username=sftp_user, password=sftp_password)
            sftp = paramiko.SFTPClient.from_transport(transport)

            # List files in SFTP directory
            try:
                files = sftp.listdir(sftp_dir)
            except Exception as e:
                return f"ERROR: Failed to list files in SFTP directory {sftp_dir}: {str(e)}"

            downloaded_files = []
            pgp_files_found = [f for f in files if f.endswith('.pgp')]

            if not pgp_files_found:
                return f"INFO: No PGP files found in SFTP directory {sftp_dir}"

            # Download PGP files
            for file in pgp_files_found:
                try:
                    local_path = os.path.join(temp_dir, file)
                    remote_path = f"{sftp_dir.rstrip('/')}/{file}"
                    sftp.get(remote_path, local_path)
                    downloaded_files.append(local_path)
                except Exception as e:
                    return f"ERROR: Failed to download file {file}: {str(e)}"

        except Exception as e:
            return f"ERROR: SFTP connection failed: {str(e)}"
        finally:
            if sftp:
                sftp.close()
            if transport:
                transport.close()

        # 4. Decrypt files with improved error handling
        try:
            # Initialize GPG with a temporary directory for better isolation
            gpg_home = os.path.join(temp_dir, '.gnupg')
            os.makedirs(gpg_home, mode=0o700)
            gpg = gnupg.GPG(gnupghome=gpg_home)

            # Import private key
            with open(private_key_local, 'r') as f:
                key_data = f.read()
            import_result = gpg.import_keys(key_data)

            if import_result.count == 0:
                return f"ERROR: Failed to import private key from {private_key_file}"

            decrypted_files = []
            for encrypted_path in downloaded_files:
                try:
                    decrypted_path = encrypted_path.replace('.pgp', '.csv')
                    with open(encrypted_path, 'rb') as f:
                        status = gpg.decrypt_file(f, passphrase=pgp_passphrase, output=decrypted_path)

                    if status.ok:
                        decrypted_files.append(decrypted_path)
                    else:
                        return f"ERROR: Decryption failed for {os.path.basename(encrypted_path)}: {status.status}"
                except Exception as e:
                    return f"ERROR: Exception during decryption of {os.path.basename(encrypted_path)}: {str(e)}"

        except Exception as e:
            return f"ERROR: GPG initialization or decryption failed: {str(e)}"

        # 5. Upload decrypted files to FILE_STRGE_STG/Inbound folder
        uploaded_files = []
        for decrypted_file in decrypted_files:
            try:
                put_result = session.file.put(decrypted_file, f"@FILE_STRGE_STG/Inbound", overwrite=True)
                uploaded_files.append(os.path.basename(decrypted_file))
            except Exception as e:
                return f"ERROR: Failed to upload {os.path.basename(decrypted_file)} to FILE_STRGE_STG/Inbound: {str(e)}"

        # 6. Load into table with improved error handling
        loaded_files = []
        for decrypted_file in decrypted_files:
            try:
                file_name = os.path.basename(decrypted_file)
                copy_sql = f"""
                    COPY INTO {target_table}
                    FROM @FILE_STRGE_STG/Inbound/{file_name}
                    FILE_FORMAT = (FORMAT_NAME = '{file_format}')
                    ON_ERROR = 'CONTINUE'
                """
                result = session.sql(copy_sql).collect()
                loaded_files.append(file_name)
            except Exception as e:
                return f"ERROR: Failed to load {file_name} into {target_table}: {str(e)}"

        return f"SUCCESS: Processed {len(pgp_files_found)} PGP files from SFTP, decrypted {len(decrypted_files)} files, uploaded {len(uploaded_files)} files to FILE_STRGE_STG/Inbound, loaded {len(loaded_files)} files into {target_table}."

    except Exception as e:
        return f"ERROR: Unexpected error in procedure: {str(e)}"
    finally:
        # Clean up temporary directory
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except Exception as e:
            # Log cleanup error but don't fail the procedure
            pass
$$;