-- Quick SFTP Connection Test
-- Replace the placeholder names with your actual Network Rule, Secret, and Integration names

-- 1. First, let's see what you have configured
SELECT 'Your Network Rules:' AS component;
SHOW NETWORK RULES;

SELECT 'Your Secrets:' AS component;
SHOW SECRETS;

SELECT 'Your External Access Integrations:' AS component;
SHOW INTEGRATIONS;

-- 2. Create a minimal test procedure that uses your External Access Integration
CREATE OR REPLACE PROCEDURE quick_sftp_test(
    integration_name STRING,
    sftp_host STRING,
    sftp_port INTEGER,
    sftp_dir STRING
)
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('paramiko')
HANDLER = 'quick_sftp_test'
-- Add your External Access Integration here
-- EXTERNAL_ACCESS_INTEGRATIONS = ('YOUR_INTEGRATION_NAME')
AS
$$
import paramiko
import json

def quick_sftp_test(session, integration_name, sftp_host, sftp_port, sftp_dir):
    """
    Quick SFTP connection test using External Access Integration
    """
    try:
        # Log start
        result_log = []
        result_log.append(f"Testing SFTP connection to {sftp_host}:{sftp_port}")
        
        # You'll need to get credentials from your secret
        # This is a placeholder - you'll need to adapt based on your secret structure
        # creds = session.sql("SELECT GET_SECRET('YOUR_SECRET_NAME')").collect()[0][0]
        # creds_json = json.loads(creds)
        # username = creds_json['username']
        # password = creds_json['password']
        
        # For now, using hardcoded values for testing
        username = 'ramchandra.kulkarni'
        password = 'MayRam@2024'
        
        result_log.append(f"Attempting connection with user: {username}")
        
        # Create transport
        transport = paramiko.Transport((sftp_host, sftp_port))
        result_log.append("Transport created successfully")
        
        # Connect
        transport.connect(username=username, password=password)
        result_log.append("Authentication successful")
        
        # Create SFTP client
        sftp = paramiko.SFTPClient.from_transport(transport)
        result_log.append("SFTP client created")
        
        # List directory
        files = sftp.listdir(sftp_dir)
        result_log.append(f"Directory listing successful: {len(files)} files found")
        
        # Check for PGP files
        pgp_files = [f for f in files if f.endswith('.pgp')]
        result_log.append(f"PGP files found: {len(pgp_files)}")
        
        if pgp_files:
            result_log.append(f"PGP files: {', '.join(pgp_files[:5])}")  # Show first 5
        
        # Clean up
        sftp.close()
        transport.close()
        result_log.append("Connection closed successfully")
        
        return "SUCCESS: " + " | ".join(result_log)
        
    except Exception as e:
        error_msg = f"ERROR: {str(e)}"
        result_log.append(error_msg)
        return " | ".join(result_log)
$$;

-- 3. Test the connection (update with your actual integration name)
-- IMPORTANT: Replace 'YOUR_INTEGRATION_NAME' with your actual integration name
/*
CALL quick_sftp_test(
    'YOUR_INTEGRATION_NAME',          -- Replace with your actual integration name
    '*************',                  -- Your SFTP host
    22,                               -- SFTP port
    '/Users/<USER>/sftp_uploads/Outbound'  -- Directory
);
*/

-- 4. Enhanced SFTP test with detailed error handling and logging
CREATE OR REPLACE PROCEDURE enhanced_sftp_test()
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('snowflake-snowpark-python', 'paramiko')
HANDLER = 'enhanced_sftp_test'
AS
$$
import paramiko
import socket
import time

def enhanced_sftp_test(session):
    transport = None
    sftp = None

    try:
        # Step 1: Test basic socket connection first
        session.sql("INSERT INTO debug_log VALUES ('INFO', 'Testing socket connection...', CURRENT_TIMESTAMP())").collect()

        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(30)  # 30 second timeout
        result = sock.connect_ex(('us-east-1.sftpcloud.io', 22))
        sock.close()

        if result != 0:
            return f"ERROR: Socket connection failed with code {result}"

        session.sql("INSERT INTO debug_log VALUES ('SUCCESS', 'Socket connection successful', CURRENT_TIMESTAMP())").collect()

        # Step 2: Create transport with timeout settings
        session.sql("INSERT INTO debug_log VALUES ('INFO', 'Creating paramiko transport...', CURRENT_TIMESTAMP())").collect()

        transport = paramiko.Transport(('us-east-1.sftpcloud.io', 22))
        transport.set_keepalive(30)

        session.sql("INSERT INTO debug_log VALUES ('SUCCESS', 'Transport created', CURRENT_TIMESTAMP())").collect()

        # Step 3: Connect with authentication
        session.sql("INSERT INTO debug_log VALUES ('INFO', 'Attempting authentication...', CURRENT_TIMESTAMP())").collect()

        transport.connect(
            username='a4877572ac6a4f17916cfd14d7009b6e',
            password='5MnDgP3GNYVGzIPvChqF4ZBfKVdtUgq2',
            timeout=30
        )

        session.sql("INSERT INTO debug_log VALUES ('SUCCESS', 'Authentication successful', CURRENT_TIMESTAMP())").collect()

        # Step 4: Create SFTP client
        session.sql("INSERT INTO debug_log VALUES ('INFO', 'Creating SFTP client...', CURRENT_TIMESTAMP())").collect()

        sftp = paramiko.SFTPClient.from_transport(transport)

        session.sql("INSERT INTO debug_log VALUES ('SUCCESS', 'SFTP client created', CURRENT_TIMESTAMP())").collect()

        # Step 5: Test directory listing
        session.sql("INSERT INTO debug_log VALUES ('INFO', 'Testing directory listing...', CURRENT_TIMESTAMP())").collect()

        files = sftp.listdir('Outbound/')

        session.sql(f"INSERT INTO debug_log VALUES ('SUCCESS', 'Found {len(files)} files in Outbound/', CURRENT_TIMESTAMP())").collect()

        # Log first few files
        for i, file in enumerate(files[:3]):
            session.sql(f"INSERT INTO debug_log VALUES ('INFO', 'File {i+1}: {file}', CURRENT_TIMESTAMP())").collect()

        return f"SUCCESS: Connected and found {len(files)} files in Outbound/"

    except paramiko.AuthenticationException as e:
        session.sql(f"INSERT INTO debug_log VALUES ('ERROR', 'Authentication failed: {str(e)}', CURRENT_TIMESTAMP())").collect()
        return f"ERROR: Authentication failed - {str(e)}"

    except paramiko.SSHException as e:
        session.sql(f"INSERT INTO debug_log VALUES ('ERROR', 'SSH error: {str(e)}', CURRENT_TIMESTAMP())").collect()
        return f"ERROR: SSH error - {str(e)}"

    except socket.error as e:
        session.sql(f"INSERT INTO debug_log VALUES ('ERROR', 'Socket error: {str(e)}', CURRENT_TIMESTAMP())").collect()
        return f"ERROR: Socket error - {str(e)}"

    except Exception as e:
        session.sql(f"INSERT INTO debug_log VALUES ('ERROR', 'Unexpected error: {str(e)}', CURRENT_TIMESTAMP())").collect()
        return f"ERROR: Unexpected error - {str(e)}"

    finally:
        # Clean up resources
        try:
            if sftp:
                sftp.close()
                session.sql("INSERT INTO debug_log VALUES ('INFO', 'SFTP client closed', CURRENT_TIMESTAMP())").collect()
        except:
            pass

        try:
            if transport:
                transport.close()
                session.sql("INSERT INTO debug_log VALUES ('INFO', 'Transport closed', CURRENT_TIMESTAMP())").collect()
        except:
            pass
$$;

-- Create debug log table
CREATE OR REPLACE TABLE debug_log (
    log_level VARCHAR(20),
    message VARCHAR(2000),
    timestamp TIMESTAMP
);

-- Alternative basic test with your cloud SFTP
CREATE OR REPLACE PROCEDURE basic_sftp_test()
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('snowflake-snowpark-python','paramiko')
HANDLER = 'basic_sftp_test'
AS
$$
import paramiko
import time

def basic_sftp_test(session):
    try:
        # Add small delay to avoid resource conflicts
        time.sleep(1)

        # Test connection to your cloud SFTP server
        transport = paramiko.Transport(('us-east-1.sftpcloud.io', 22))
        transport.connect(
            username='a4877572ac6a4f17916cfd14d7009b6e',
            password='5MnDgP3GNYVGzIPvChqF4ZBfKVdtUgq2',
            timeout=30
        )

        sftp = paramiko.SFTPClient.from_transport(transport)
        files = sftp.listdir('Outbound/')

        sftp.close()
        transport.close()

        return f"SUCCESS: Connected and found {len(files)} files"

    except Exception as e:
        return f"ERROR: {str(e)}"
$$;

-- 5. Run basic test first
CALL basic_sftp_test();

-- 6. Check execution results
SELECT 
    start_time,
    execution_status,
    error_message,
    total_elapsed_time/1000 AS duration_seconds
FROM TABLE(INFORMATION_SCHEMA.QUERY_HISTORY())
WHERE query_text ILIKE '%sftp_test%'
    AND start_time >= DATEADD(minute, -10, CURRENT_TIMESTAMP())
ORDER BY start_time DESC;

-- 7. Test with public SFTP server for comparison
CREATE OR REPLACE PROCEDURE test_public_sftp()
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('paramiko')
HANDLER = 'test_public_sftp'
AS
$$
import paramiko

def test_public_sftp(session):
    try:
        # Test with known working public SFTP server
        transport = paramiko.Transport(('test.rebex.net', 22))
        transport.connect(username='demo', password='password')
        
        sftp = paramiko.SFTPClient.from_transport(transport)
        files = sftp.listdir('/pub/example')
        
        sftp.close()
        transport.close()
        
        return f"SUCCESS: Public SFTP test passed - found {len(files)} files"
        
    except Exception as e:
        return f"ERROR in public SFTP test: {str(e)}"
$$;

-- 8. Run public SFTP test
CALL test_public_sftp();

-- 9. Compare results
SELECT 'Test Results Comparison:' AS comparison;
SELECT 
    CASE 
        WHEN query_text ILIKE '%basic_sftp_test%' THEN 'Your SFTP Server'
        WHEN query_text ILIKE '%test_public_sftp%' THEN 'Public SFTP Server'
        ELSE 'Other'
    END AS test_type,
    execution_status,
    error_message,
    start_time
FROM TABLE(INFORMATION_SCHEMA.QUERY_HISTORY())
WHERE (query_text ILIKE '%basic_sftp_test%' OR query_text ILIKE '%test_public_sftp%')
    AND start_time >= DATEADD(minute, -10, CURRENT_TIMESTAMP())
ORDER BY start_time DESC;

-- 10. Next steps based on results
SELECT '
INTERPRETING RESULTS:

1. If PUBLIC SFTP TEST PASSES but YOUR SFTP TEST FAILS:
   - Network connectivity issue with your server
   - Your server may not be accessible from Snowflake
   - Check firewall, router settings, or use ngrok/port forwarding

2. If BOTH TESTS FAIL:
   - External Access Integration may not be properly configured
   - Network Rule may be blocking connections
   - Check your Snowflake networking setup

3. If YOUR SFTP TEST PASSES:
   - Great! Your setup is working
   - Proceed with the full PGP procedure

4. If you see "Network is unreachable":
   - Your SFTP server is not accessible from internet
   - Consider using ngrok, port forwarding, or cloud SFTP

NEXT STEPS:
- Review the test results above
- Check your Network Rule allows the SFTP host/port
- Verify your Secret contains correct credentials
- Ensure External Access Integration references both correctly

' AS next_steps;
