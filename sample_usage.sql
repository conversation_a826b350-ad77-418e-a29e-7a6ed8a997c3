-- Sample usage of the load_pgp_from_sftp procedure
-- Updated for your existing FILE_STRGE_STG infrastructure
-- Make sure to replace the parameter values with your actual configuration

-- Example 1: Basic usage with your existing setup
CALL load_pgp_from_sftp(
    'your-sftp-server.com',           -- sftp_host
    22,                               -- sftp_port
    'your_sftp_username',             -- sftp_user
    'your_sftp_password',             -- sftp_password
    '/path/to/pgp/files',             -- sftp_dir
    'pgp_private_key.asc',            -- private_key_file (your existing key in FILE_STRGE_STG/Secrets)
    'your_target_table',              -- target_table
    'CSV_FORMAT'                      -- file_format (your CSV file format name)
);

-- Example 2: Usage with different data source
CALL load_pgp_from_sftp(
    'sftp.example.com',
    22,
    'datauser',
    'secure_password',
    '/incoming/encrypted',
    'pgp_private_key.asc',            -- Uses your existing private key
    'raw_data.customer_files',
    'PIPE_DELIMITED_FORMAT'
);

-- Example 3: If you have multiple private keys in Secrets folder
CALL load_pgp_from_sftp(
    'partner-sftp.com',
    22,
    'partner_user',
    'partner_password',
    '/data/encrypted',
    'partner_private_key.asc',        -- Different private key for different partner (must exist in FILE_STRGE_STG/Secrets)
    'staging.partner_data',
    'CSV_FORMAT'
);

-- Prerequisites verification (your existing setup):

-- 1. Verify your passphrase table exists and has data
SELECT COUNT(*) as passphrase_count FROM edw_db.dims.pgp_passphrase_table;
SELECT passphrase FROM edw_db.dims.pgp_passphrase_table LIMIT 1; -- Verify passphrase exists

-- 2. Verify your FILE_STRGE_STG stage and folders exist
LIST @FILE_STRGE_STG;
LIST @FILE_STRGE_STG/Secrets;
LIST @FILE_STRGE_STG/Inbound;
LIST @FILE_STRGE_STG/Outbound;

-- 3. Verify your private key exists in Secrets folder
LIST @FILE_STRGE_STG/Secrets PATTERN='.*pgp_private_key.asc.*';

-- 4. If you need to update your passphrase (replace with actual passphrase)
-- UPDATE edw_db.dims.pgp_passphrase_table
-- SET passphrase = 'your_actual_pgp_passphrase_here'
-- WHERE id = 1;

-- 4. Create a file format for your CSV files
CREATE OR REPLACE FILE FORMAT CSV_FORMAT
    TYPE = 'CSV'
    FIELD_DELIMITER = ','
    RECORD_DELIMITER = '\n'
    SKIP_HEADER = 1
    FIELD_OPTIONALLY_ENCLOSED_BY = '"'
    TRIM_SPACE = TRUE
    ERROR_ON_COLUMN_COUNT_MISMATCH = FALSE
    ESCAPE = 'NONE'
    ESCAPE_UNENCLOSED_FIELD = '\134'
    DATE_FORMAT = 'AUTO'
    TIMESTAMP_FORMAT = 'AUTO'
    NULL_IF = ('\\N', 'NULL', 'null', '')
    COMMENT = 'CSV format for loading decrypted PGP files';

-- 5. Your private keys are already uploaded to FILE_STRGE_STG/Secrets
-- Verify they exist:
-- pgp_private_key.asc and pgp_public_key.asc should be in @FILE_STRGE_STG/Secrets

-- 6. Create your target table (example structure)
CREATE TABLE IF NOT EXISTS your_target_table (
    column1 VARCHAR(100),
    column2 VARCHAR(100),
    column3 NUMBER,
    column4 DATE,
    load_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
);

-- Monitoring and troubleshooting queries:

-- Check if passphrase exists
SELECT * FROM edw_db.dims.pgp_passphrase_table;

-- List files in your FILE_STRGE_STG folders
LIST @FILE_STRGE_STG/Secrets;
LIST @FILE_STRGE_STG/Inbound;
LIST @FILE_STRGE_STG/Outbound;

-- Check procedure execution history
SELECT *
FROM TABLE(INFORMATION_SCHEMA.QUERY_HISTORY())
WHERE QUERY_TEXT LIKE '%load_pgp_from_sftp%'
ORDER BY START_TIME DESC
LIMIT 10;

-- View procedure definition
SHOW PROCEDURES LIKE 'load_pgp_from_sftp';
