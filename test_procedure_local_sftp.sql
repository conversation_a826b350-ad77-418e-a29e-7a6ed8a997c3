-- Test Script for PGP SFTP Load Procedure
-- Using your local Mac SFTP server for testing

-- 1. Set the context
USE DATABASE edw_db;
USE SCHEMA dims;

-- 2. Create a test target table for loading the decrypted data
CREATE OR REPLACE TABLE test_pgp_load_table (
    record_id NUMBER AUTOINCREMENT PRIMARY KEY,
    column1 VARCHAR(500),
    column2 VARCHAR(500),
    column3 VARCHAR(500),
    column4 VARCHAR(500),
    column5 VARCHAR(500),
    file_name VARCHAR(255),
    load_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
);

-- 3. Verify your setup before testing
SELECT 'Pre-test verification' AS check_type;

-- Check passphrase table
SELECT COUNT(*) AS passphrase_count FROM edw_db.dims.pgp_passphrase_table;

-- Check FILE_STRGE_STG/Secrets for private key
LIST @FILE_STRGE_STG/Secrets PATTERN='.*pgp_private_key.asc.*';

-- Check if CSV_FORMAT exists
SHOW FILE FORMATS LIKE 'CSV_FORMAT';

-- 4. Test call to your procedure with local SFTP server details
CALL load_pgp_from_sftp(
    '*************',                  -- sftp_host (your local Mac IP)
    22,                               -- sftp_port
    'ramchandra.kulkarni',            -- sftp_user (your Mac username)
    'MayRam@2024',                    -- sftp_password (your Mac password)
    '/Users/<USER>/sftp_uploads/Outbound',  -- sftp_dir (where PGP files are located)
    'pgp_private_key.asc',            -- private_key_file (your existing key in FILE_STRGE_STG/Secrets)
    'test_pgp_load_table',            -- target_table (our test table)
    'CSV_FORMAT'                      -- file_format
);

-- 5. Check results after procedure execution
SELECT 'Post-execution verification' AS check_type;

-- Check if files were uploaded to FILE_STRGE_STG/Inbound
LIST @FILE_STRGE_STG/Inbound;

-- Check if data was loaded into the test table
SELECT COUNT(*) AS records_loaded FROM test_pgp_load_table;

-- View sample data (first 10 records)
SELECT * FROM test_pgp_load_table LIMIT 10;

-- Check load timestamps
SELECT 
    COUNT(*) AS total_records,
    MIN(load_timestamp) AS first_load,
    MAX(load_timestamp) AS last_load
FROM test_pgp_load_table;

-- 6. Alternative test with different target table (if needed)
/*
CREATE OR REPLACE TABLE test_pgp_load_table_2 (
    id NUMBER AUTOINCREMENT PRIMARY KEY,
    data_field_1 VARCHAR(1000),
    data_field_2 VARCHAR(1000),
    data_field_3 NUMBER,
    data_field_4 DATE,
    source_file VARCHAR(255),
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
);

CALL load_pgp_from_sftp(
    '*************',
    22,
    'ramchandra.kulkarni',
    'MayRam@2024',
    '/Users/<USER>/sftp_uploads/Outbound',
    'pgp_private_key.asc',
    'test_pgp_load_table_2',
    'CSV_FORMAT'
);
*/

-- 7. Cleanup commands (run after testing if needed)
/*
-- Clean up test data
TRUNCATE TABLE test_pgp_load_table;

-- Remove test files from Inbound stage
REMOVE @FILE_STRGE_STG/Inbound PATTERN='.*\.csv';

-- Drop test table
DROP TABLE test_pgp_load_table;
*/

-- 8. Troubleshooting Network Connectivity Issues

-- The error "Network is unreachable" means Snowflake cannot connect to your local Mac
-- This is because your Mac is behind a router/firewall and not accessible from the internet

-- SOLUTIONS:

-- Option 1: Use a public SFTP server for testing
-- You can use a free SFTP testing service like:
-- - test.rebex.net (public test server)
-- - dlptest.com (free SFTP test server)

-- Option 2: Set up port forwarding on your router
-- Forward port 22 from your router's public IP to your Mac's local IP (*************)
-- Then use your router's public IP instead of *************

-- Option 3: Use ngrok to create a tunnel (recommended for testing)
-- Install ngrok: brew install ngrok
-- Run: ngrok tcp 22
-- Use the ngrok URL and port in your procedure call

-- Option 4: Test with a cloud-based SFTP server
-- Set up a temporary SFTP server on AWS, Azure, or Google Cloud

-- Check recent procedure executions for detailed error messages
SELECT
    start_time,
    total_elapsed_time/1000 AS duration_seconds,
    execution_status,
    error_message
FROM TABLE(INFORMATION_SCHEMA.QUERY_HISTORY())
WHERE query_text ILIKE '%load_pgp_from_sftp%'
    AND start_time >= DATEADD(hour, -1, CURRENT_TIMESTAMP())
ORDER BY start_time DESC
LIMIT 5;

-- IMMEDIATE TESTING SOLUTION: Use ngrok
-- 1. Install ngrok: brew install ngrok
-- 2. Run: ngrok tcp 22
-- 3. Note the forwarding address (e.g., 0.tcp.ngrok.io:12345)
-- 4. Update the procedure call below:

/*
CALL load_pgp_from_sftp(
    '0.tcp.ngrok.io',                 -- Use ngrok hostname
    12345,                            -- Use ngrok port
    'ramchandra.kulkarni',            -- Your Mac username
    'MayRam@2024',                    -- Your Mac password
    '/Users/<USER>/sftp_uploads/Outbound',
    'pgp_private_key.asc',
    'BOOKINGS_D',
    'CSV_FORMAT'
);
*/
