# FileZilla SFTP Server Setup for Snowflake Testing

## Option 1: FileZilla Server (Recommended)

### Step 1: Download and Install FileZilla Server
1. Download FileZilla Server from: https://filezilla-project.org/download.php?type=server
2. Install FileZilla Server on your Mac
3. Launch FileZilla Server

### Step 2: Configure FileZilla Server
1. **Open FileZilla Server Interface**
2. **Create a User Account**:
   - Go to `Edit` → `Users`
   - Click `Add` to create new user
   - Username: `snowflake_test`
   - Password: `SnowTest2024!`

3. **Set Home Directory**:
   - Select the user `snowflake_test`
   - Go to `Shared folders` tab
   - Add directory: `/Users/<USER>/sftp_uploads`
   - Check `Read`, `Write`, `Delete`, `Append` permissions

4. **Configure Server Settings**:
   - Go to `Edit` → `Settings`
   - Set `Listen on port`: 21 (FTP) or 22 (SFTP)
   - Enable passive mode
   - Set passive port range: 50000-50100

### Step 3: Test FileZilla Connection
```bash
# Test FTP connection
ftp *************
# Username: snowflake_test
# Password: SnowTest2024!

# Or test with FileZilla Client
# Host: *************
# Username: snowflake_test
# Password: SnowTest2024!
# Port: 21
```

### Step 4: Updated Snowflake Procedure Call
```sql
CALL load_pgp_from_sftp(
    '*************',                  -- Your Mac IP
    21,                               -- FTP port (or 22 for SFTP)
    'snowflake_test',                 -- FileZilla user
    'SnowTest2024!',                  -- FileZilla password
    '/Outbound',                      -- Relative path in FileZilla
    'pgp_private_key.asc',
    'BOOKINGS_D',
    'CSV_FORMAT'
);
```

## Option 2: Use Cloud-Based SFTP (Alternative)

If local FileZilla still has network issues, consider these cloud options:

### A. AWS Transfer Family (SFTP)
1. Create AWS SFTP server
2. Get public endpoint
3. Use in Snowflake procedure

### B. Google Cloud Storage SFTP
1. Set up Cloud Storage with SFTP access
2. Get public IP/hostname
3. Use in Snowflake procedure

### C. Free Online SFTP Services
- **SFTPGo Cloud**: https://sftpgo.com/
- **Files.com**: https://www.files.com/
- **ExaVault**: https://www.exavault.com/

## Option 3: Router Configuration (Port Forwarding)

### Step 1: Find Your Public IP
```bash
curl ifconfig.me
# Note your public IP (e.g., ***********)
```

### Step 2: Configure Router Port Forwarding
1. Access your router admin panel (usually ***********)
2. Find "Port Forwarding" or "Virtual Server" settings
3. Add rule:
   - External Port: 2222
   - Internal IP: *************
   - Internal Port: 22
   - Protocol: TCP

### Step 3: Test External Access
```bash
# Test from outside your network (use mobile hotspot)
ssh -p 2222 ramchandra.kulkarni@[YOUR_PUBLIC_IP]
```

### Step 4: Updated Procedure Call
```sql
CALL load_pgp_from_sftp(
    '[YOUR_PUBLIC_IP]',               -- Your router's public IP
    2222,                             -- Forwarded port
    'ramchandra.kulkarni',
    'MayRam@2024',
    '/Users/<USER>/sftp_uploads/Outbound',
    'pgp_private_key.asc',
    'BOOKINGS_D',
    'CSV_FORMAT'
);
```

## Quick Test with Free SFTP Service

### Using test.rebex.net (Public Test Server)
```sql
-- First, upload your .pgp files to test.rebex.net using FileZilla Client
-- Host: test.rebex.net
-- Username: demo
-- Password: password
-- Upload to: /pub/example/

CALL load_pgp_from_sftp(
    'test.rebex.net',
    22,
    'demo',
    'password',
    '/pub/example',
    'pgp_private_key.asc',
    'BOOKINGS_D',
    'CSV_FORMAT'
);
```

## Recommended Testing Approach

1. **Start with FileZilla Server** on your Mac
2. **If network issues persist**, try port forwarding
3. **For immediate testing**, use test.rebex.net
4. **For production-like testing**, set up cloud SFTP

## FileZilla Server Troubleshooting

### Common Issues:
1. **Firewall blocking**: Add FileZilla Server to Mac firewall exceptions
2. **Port conflicts**: Use different port (e.g., 2121 instead of 21)
3. **Permission issues**: Ensure FileZilla user has proper folder permissions

### Mac Firewall Configuration:
```bash
# Check firewall status
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --getglobalstate

# Add FileZilla to firewall exceptions
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --add /Applications/FileZilla\ Server.app
```

## Next Steps

1. **Try FileZilla Server setup first**
2. **Test locally with FileZilla Client**
3. **If successful locally, test from Snowflake**
4. **If network issues persist, try cloud SFTP option**

Let me know which approach you'd like to try first!
